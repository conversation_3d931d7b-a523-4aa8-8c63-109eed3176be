#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البحث اليدوي عن المواقع الرسمية للمصادر الإخبارية العراقية المهمة
"""

from database import DatabaseManager

def add_known_websites():
    """إضافة المواقع المعروفة للمصادر المهمة"""
    db = DatabaseManager()
    
    # قائمة المواقع المعروفة للمصادر المهمة
    known_websites = {
        "وكالة السومرية نيوز": "https://www.alsumaria.tv",
        "شبكة الجزيرة الإعلامية": "https://www.aljazeera.net",
        "قناة العربية الحدث": "https://www.alarabiya.net",
        "قناة mbc عراق الفضائية": "https://www.mbc.net/ar/mbc-iraq",
        "قناة روسيا اليوم الفضائية": "https://arabic.rt.com",
        "التلفزيون الصيني المركزي cctv": "https://arabic.cgtn.com",
        "قناة الشرق الفضائية": "https://www.alsharq.com",
        "شبكة التلفزيون العربي": "https://www.alaraby.tv",
        "وكالة شفق نيوز": "https://shafaq.com",
        "وكالة كركوك نيوز": "https://kirkuknews.com",
        "وكالة المسلة": "https://almesalla.com",
        "وكالة بغداد اليوم الإخبارية": "https://baghdadtoday.news",
        "قناة النهرين الإخبارية الرقمية": "https://www.nahrain.tv",
        "موقع قناة النهرين الفضائية": "https://www.nahrain.tv",
        "وكالة كربلاء 24 الإخبارية": "https://karbala24.net",
        "وكالة البينة الإخبارية": "https://albayyna-new.com",
        "وكالة العراق الان نيوز": "https://iraqnow.news",
        "قناة الفلوجة الفضائية": "https://fallujatv.com",
        "صحيفة نفط العراق": "https://iraqoilnews.com",
        "وكالة الخبر العراقي": "https://iraqnews.com"
    }
    
    print("🌐 إضافة المواقع المعروفة للمصادر المهمة...")
    print("=" * 60)
    
    updated_count = 0
    not_found_count = 0
    
    # جلب جميع المصادر
    sources = db.get_all_sources()
    source_dict = {source[1]: source for source in sources}  # اسم المصدر -> بيانات المصدر
    
    for source_name, website in known_websites.items():
        if source_name in source_dict:
            source_data = source_dict[source_name]
            source_id, name, current_website, email, whatsapp = source_data
            
            # تحديث الموقع إذا لم يكن موجود
            if not current_website or not current_website.strip():
                success = db.update_source(
                    source_id=source_id,
                    name=name,
                    website=website,
                    email=email,
                    whatsapp=whatsapp
                )
                
                if success:
                    print(f"✅ {source_name}")
                    print(f"   🌐 {website}")
                    updated_count += 1
                else:
                    print(f"❌ فشل في تحديث: {source_name}")
            else:
                print(f"⏭️  {source_name} - يوجد موقع مسبقاً: {current_website}")
        else:
            print(f"❓ لم يتم العثور على: {source_name}")
            not_found_count += 1
    
    print("\n" + "=" * 60)
    print("📊 ملخص العملية:")
    print(f"   ✅ تم تحديثها: {updated_count}")
    print(f"   ❓ غير موجودة: {not_found_count}")
    
    if updated_count > 0:
        print(f"\n🎉 تم تحديث {updated_count} مصدر بمواقعهم الرسمية!")

def search_specific_sources():
    """البحث عن مصادر محددة"""
    from web_search import web_search
    import time
    
    db = DatabaseManager()
    
    # مصادر للبحث عنها
    search_targets = [
        "وكالة اليوم الإخبارية العراق",
        "وكالة موسوعة الرافدين",
        "وكالة البوابة الإخبارية العراق",
        "وكالة نواة الإخبارية العراق",
        "وكالة سنا الإخبارية العراق"
    ]
    
    print("\n🔍 البحث عن مصادر محددة...")
    print("=" * 50)
    
    for search_term in search_targets:
        print(f"\n🔎 البحث عن: {search_term}")
        
        try:
            results = web_search(search_term, num_results=3)
            
            if results:
                print("   📋 النتائج:")
                for i, result in enumerate(results, 1):
                    print(f"   {i}. {result.get('title', 'بدون عنوان')}")
                    print(f"      🌐 {result.get('url', 'بدون رابط')}")
                    if result.get('snippet'):
                        snippet = result['snippet'][:100] + "..." if len(result['snippet']) > 100 else result['snippet']
                        print(f"      📝 {snippet}")
                    print()
            else:
                print("   ❌ لم يتم العثور على نتائج")
            
            time.sleep(2)  # توقف بين البحثات
            
        except Exception as e:
            print(f"   ❌ خطأ في البحث: {e}")

def main():
    """الدالة الرئيسية"""
    print("🌐 البحث اليدوي عن المواقع الرسمية")
    print("=" * 50)
    
    print("اختر العملية:")
    print("1. إضافة المواقع المعروفة")
    print("2. البحث عن مصادر محددة")
    print("3. كلاهما")
    
    try:
        choice = input("\nأدخل اختيارك (1-3): ").strip()
        
        if choice == "1":
            add_known_websites()
        elif choice == "2":
            search_specific_sources()
        elif choice == "3":
            add_known_websites()
            search_specific_sources()
        else:
            print("اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
