import tkinter as tk
from tkinter import ttk, messagebox
from database import DatabaseManager
from models import MediaSource
from gui.add_edit_dialog import AddEditDialog
from gui.styles import AppStyles

class MainWindow:
    """النافذة الرئيسية لإدارة المصادر الإخبارية"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.db = DatabaseManager()
        
        # إعداد النافذة الرئيسية
        self.setup_window()
        
        # تكوين الأنماط
        AppStyles.configure_styles()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات
        self.refresh_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("إدارة المصادر الإخبارية")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # توسيط النافذة
        self.center_window()
        
        # تكوين الخلفية
        self.root.configure(bg=AppStyles.BACKGROUND_COLOR)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الهيدر
        self.create_header(main_frame)
        
        # إنشاء شريط الأدوات
        self.create_toolbar(main_frame)
        
        # إنشاء الجدول
        self.create_table(main_frame)
        
        # إنشاء شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء الهيدر"""
        header_frame = ttk.Frame(parent, style="Card.TFrame", padding=15)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # العنوان الرئيسي
        title_label = ttk.Label(
            header_frame, 
            text="🗞️ إدارة المصادر الإخبارية", 
            style="Title.TLabel"
        )
        title_label.pack(side=tk.LEFT)
        
        # معلومات إضافية
        info_label = ttk.Label(
            header_frame, 
            text="إدارة شاملة للمصادر الإخبارية والمؤسسات الإعلامية",
            foreground=AppStyles.DARK_COLOR
        )
        info_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent, style="Card.TFrame", padding=10)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الجانب الأيسر - أزرار العمليات
        left_frame = ttk.Frame(toolbar_frame)
        left_frame.pack(side=tk.LEFT)
        
        # زر إضافة مصدر جديد
        add_btn = ttk.Button(
            left_frame,
            text="➕ إضافة مصدر",
            command=self.add_source,
            style="Success.TButton"
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # زر تعديل
        edit_btn = ttk.Button(
            left_frame,
            text="✏️ تعديل",
            command=self.edit_source,
            style="Primary.TButton"
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # زر حذف
        delete_btn = ttk.Button(
            left_frame,
            text="🗑️ حذف",
            command=self.delete_source,
            style="Danger.TButton"
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # زر تحديث
        refresh_btn = ttk.Button(
            left_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            style="Primary.TButton"
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # الجانب الأيمن - البحث
        right_frame = ttk.Frame(toolbar_frame)
        right_frame.pack(side=tk.RIGHT)
        
        # تسمية البحث
        search_label = ttk.Label(right_frame, text="🔍 البحث:", style="Header.TLabel")
        search_label.pack(side=tk.LEFT, padx=(0, 5))
        
        # حقل البحث
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(
            right_frame,
            textvariable=self.search_var,
            style="Custom.TEntry",
            width=25
        )
        search_entry.pack(side=tk.LEFT)
    
    def create_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk.Frame(parent, style="Card.TFrame", padding=10)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # تسمية الجدول
        table_label = ttk.Label(table_frame, text="قائمة المصادر الإخبارية", style="Header.TLabel")
        table_label.pack(anchor=tk.W, pady=(0, 10))
        
        # إطار الجدول مع أشرطة التمرير
        tree_frame = ttk.Frame(table_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # تعريف الأعمدة
        columns = ("name", "website", "email", "whatsapp")
        column_headers = ("اسم المصدر", "الموقع الرسمي", "الإيميل الرسمي", "رقم واتساب")
        
        # إنشاء الجدول
        self.tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            style="Custom.Treeview",
            height=15
        )
        
        # تكوين الأعمدة
        for col, header in zip(columns, column_headers):
            self.tree.heading(col, text=header)
            self.tree.column(col, width=200, minwidth=150)
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول وأشرطة التمرير
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind("<Double-1>", lambda e: self.edit_source())
        self.tree.bind("<Delete>", lambda e: self.delete_source())
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent, style="Card.TFrame", padding=5)
        status_frame.pack(fill=tk.X)
        
        self.status_var = tk.StringVar()
        self.status_var.set("جاهز")
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)
        
        # عداد المصادر
        self.count_var = tk.StringVar()
        count_label = ttk.Label(status_frame, textvariable=self.count_var)
        count_label.pack(side=tk.RIGHT)

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # جلب البيانات من قاعدة البيانات
            sources = self.db.get_all_sources()

            # إضافة البيانات إلى الجدول
            for source_data in sources:
                source = MediaSource.from_tuple(source_data)
                self.tree.insert("", tk.END, values=source.get_display_data(), tags=(source.id,))

            # تحديث العداد
            count = len(sources)
            self.count_var.set(f"إجمالي المصادر: {count}")
            self.status_var.set("تم تحديث البيانات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")
            self.status_var.set("خطأ في تحديث البيانات")

    def add_source(self):
        """إضافة مصدر جديد"""
        dialog = AddEditDialog(self.root, title="إضافة مصدر جديد")
        result = dialog.show()

        if result:
            success = self.db.add_source(
                name=result.name,
                website=result.website,
                email=result.email,
                whatsapp=result.whatsapp
            )

            if success:
                self.refresh_data()
                messagebox.showinfo("نجح", "تم إضافة المصدر بنجاح!")
                self.status_var.set("تم إضافة مصدر جديد")
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المصدر. قد يكون الاسم موجود مسبقاً.")
                self.status_var.set("فشل في إضافة المصدر")

    def edit_source(self):
        """تعديل مصدر موجود"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مصدر للتعديل")
            return

        # الحصول على معرف المصدر
        item = selected_item[0]
        source_id = self.tree.item(item)["tags"][0]

        # جلب بيانات المصدر
        source_data = self.db.get_source_by_id(source_id)
        if not source_data:
            messagebox.showerror("خطأ", "لم يتم العثور على المصدر")
            return

        source = MediaSource.from_tuple(source_data)

        # فتح نافذة التعديل
        dialog = AddEditDialog(self.root, source=source, title="تعديل المصدر")
        result = dialog.show()

        if result:
            success = self.db.update_source(
                source_id=source_id,
                name=result.name,
                website=result.website,
                email=result.email,
                whatsapp=result.whatsapp
            )

            if success:
                self.refresh_data()
                messagebox.showinfo("نجح", "تم تحديث المصدر بنجاح!")
                self.status_var.set("تم تحديث المصدر")
            else:
                messagebox.showerror("خطأ", "فشل في تحديث المصدر")
                self.status_var.set("فشل في تحديث المصدر")

    def delete_source(self):
        """حذف مصدر"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مصدر للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا المصدر؟\nلا يمكن التراجع عن هذا الإجراء."
        )

        if not result:
            return

        # الحصول على معرف المصدر
        item = selected_item[0]
        source_id = self.tree.item(item)["tags"][0]

        # حذف المصدر
        success = self.db.delete_source(source_id)

        if success:
            self.refresh_data()
            messagebox.showinfo("نجح", "تم حذف المصدر بنجاح!")
            self.status_var.set("تم حذف المصدر")
        else:
            messagebox.showerror("خطأ", "فشل في حذف المصدر")
            self.status_var.set("فشل في حذف المصدر")

    def on_search_change(self, *args):
        """البحث في المصادر"""
        search_term = self.search_var.get().strip()

        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        if search_term:
            # البحث في قاعدة البيانات
            sources = self.db.search_sources(search_term)
            self.status_var.set(f"نتائج البحث عن: {search_term}")
        else:
            # عرض جميع المصادر
            sources = self.db.get_all_sources()
            self.status_var.set("عرض جميع المصادر")

        # إضافة النتائج إلى الجدول
        for source_data in sources:
            source = MediaSource.from_tuple(source_data)
            self.tree.insert("", tk.END, values=source.get_display_data(), tags=(source.id,))

        # تحديث العداد
        count = len(sources)
        self.count_var.set(f"عدد النتائج: {count}")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
