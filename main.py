#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة المصادر الإخبارية
تطبيق مكتبي لإدارة المصادر الإخبارية والمؤسسات الإعلامية

المطور: مساعد الذكي
التاريخ: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui.main_window import MainWindow
    from database import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def check_dependencies():
    """التحقق من توفر المتطلبات"""
    try:
        import tkinter
        import sqlite3
        return True
    except ImportError as e:
        messagebox.showerror(
            "خطأ في المتطلبات",
            f"المتطلبات غير متوفرة: {e}\n"
            "يرجى التأكد من تثبيت Python مع tkinter"
        )
        return False

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        db = DatabaseManager()
        return True
    except Exception as e:
        messagebox.showerror(
            "خطأ في قاعدة البيانات",
            f"فشل في تهيئة قاعدة البيانات: {e}"
        )
        return False

def main():
    """الدالة الرئيسية للتطبيق"""
    # التحقق من المتطلبات
    if not check_dependencies():
        return
    
    # تهيئة قاعدة البيانات
    if not initialize_database():
        return
    
    try:
        # إنشاء وتشغيل التطبيق
        app = MainWindow()
        app.run()
        
    except Exception as e:
        messagebox.showerror(
            "خطأ في التطبيق",
            f"حدث خطأ غير متوقع: {e}"
        )
        print(f"خطأ: {e}")

if __name__ == "__main__":
    main()
