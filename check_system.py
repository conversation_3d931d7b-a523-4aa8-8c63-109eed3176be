#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام للتأكد من جاهزية تشغيل برنامج إدارة المصادر الإخبارية
"""

import sys
import os
import platform

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    print(f"   الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 6:
        print("   ✅ إصدار Python مناسب")
        return True
    else:
        print("   ❌ يتطلب Python 3.6 أو أحدث")
        return False

def check_tkinter():
    """فحص توفر tkinter"""
    print("\n🖼️ فحص مكتبة tkinter...")
    try:
        import tkinter
        print("   ✅ tkinter متوفرة")
        return True
    except ImportError:
        print("   ❌ tkinter غير متوفرة")
        print("   💡 قم بتثبيت python3-tk على Linux أو تأكد من تثبيت Python كاملاً")
        return False

def check_sqlite():
    """فحص توفر sqlite3"""
    print("\n🗄️ فحص مكتبة sqlite3...")
    try:
        import sqlite3
        print("   ✅ sqlite3 متوفرة")
        return True
    except ImportError:
        print("   ❌ sqlite3 غير متوفرة")
        return False

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة...")
    
    required_files = [
        "main.py",
        "database.py", 
        "models.py",
        "gui/main_window.py",
        "gui/add_edit_dialog.py",
        "gui/styles.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} مفقود")
            all_exist = False
    
    return all_exist

def check_permissions():
    """فحص صلاحيات الكتابة"""
    print("\n🔐 فحص صلاحيات الكتابة...")
    
    try:
        # محاولة إنشاء ملف تجريبي
        test_file = "test_permissions.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        
        # حذف الملف التجريبي
        os.remove(test_file)
        
        print("   ✅ صلاحيات الكتابة متوفرة")
        return True
    except Exception as e:
        print(f"   ❌ مشكلة في صلاحيات الكتابة: {e}")
        return False

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗃️ فحص قاعدة البيانات...")
    
    try:
        from database import DatabaseManager
        db = DatabaseManager()
        
        # اختبار العمليات الأساسية
        sources = db.get_all_sources()
        print(f"   ✅ قاعدة البيانات تعمل بشكل صحيح ({len(sources)} مصدر)")
        return True
    except Exception as e:
        print(f"   ❌ مشكلة في قاعدة البيانات: {e}")
        return False

def get_system_info():
    """عرض معلومات النظام"""
    print("\n💻 معلومات النظام:")
    print(f"   نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"   المعمارية: {platform.machine()}")
    print(f"   Python: {platform.python_version()}")
    print(f"   المجلد الحالي: {os.getcwd()}")

def main():
    """الدالة الرئيسية للفحص"""
    print("🔍 فحص جاهزية النظام لتشغيل برنامج إدارة المصادر الإخبارية")
    print("=" * 60)
    
    # عرض معلومات النظام
    get_system_info()
    
    # تشغيل الفحوصات
    checks = [
        check_python_version(),
        check_tkinter(),
        check_sqlite(),
        check_files(),
        check_permissions(),
        check_database()
    ]
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if all(checks):
        print("🎉 النظام جاهز! يمكنك تشغيل البرنامج بأمان")
        print("\n🚀 لتشغيل البرنامج:")
        print("   python main.py")
        print("   أو")
        print("   run.bat (Windows)")
        print("   ./run.sh (Linux/Mac)")
        
        print("\n📊 لإضافة بيانات تجريبية:")
        print("   python sample_data.py")
        
        print("\n🧪 لتشغيل الاختبارات:")
        print("   python test_app.py")
        
    else:
        print("❌ يوجد مشاكل تحتاج إلى حل قبل تشغيل البرنامج")
        print("\n💡 راجع الرسائل أعلاه لحل المشاكل")
    
    return all(checks)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
