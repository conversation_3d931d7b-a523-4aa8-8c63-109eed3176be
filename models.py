from dataclasses import dataclass
from typing import Optional

@dataclass
class MediaSource:
    """نموذج بيانات المصدر الإخباري"""
    id: Optional[int] = None
    name: str = ""
    website: str = ""
    email: str = ""
    whatsapp: str = ""
    
    def __post_init__(self):
        """التحقق من صحة البيانات"""
        if not self.name.strip():
            raise ValueError("اسم المصدر مطلوب")
    
    def to_tuple(self) -> tuple:
        """تحويل إلى tuple للعرض في الجدول"""
        return (self.id, self.name, self.website, self.email, self.whatsapp)
    
    @classmethod
    def from_tuple(cls, data: tuple) -> 'MediaSource':
        """إنشاء كائن من tuple"""
        if len(data) >= 5:
            return cls(
                id=data[0],
                name=data[1],
                website=data[2],
                email=data[3],
                whatsapp=data[4]
            )
        return cls()
    
    def is_valid(self) -> bool:
        """التحقق من صحة البيانات"""
        return bool(self.name.strip())
    
    def get_display_data(self) -> tuple:
        """الحصول على البيانات للعرض (بدون ID)"""
        return (self.name, self.website, self.email, self.whatsapp)
