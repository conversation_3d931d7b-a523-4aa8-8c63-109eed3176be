#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث المواقع الإضافية التي تم العثور عليها
"""

from database import DatabaseManager

def update_additional_websites():
    """تحديث المواقع الإضافية"""
    db = DatabaseManager()
    
    # المواقع الإضافية التي تم العثور عليها
    additional_websites = {
        "وكالة اليوم الإخبارية": "https://today-agency.net",
        "وكالة موسوعة الرافدين": "https://alrafidain.news",
        "الوكالة الوطنية العراقية للأنباء": "https://www.ninanews.com",
        "وكالة البوابة الإخبارية": "https://albawaba.com",
        "وكالة نواة الإخبارية": "https://nawanews.com",
        "وكالة سنا الإخبارية": "https://sananews.net",
        "وكالة الرصافي نيوز": "https://alrasafi.com",
        "وكالة الرصد برس": "https://alrasadpress.com",
        "وكالة هنا العراق الإخبارية الدولية": "https://hna-iraq.net",
        "وكالة بصمة نيوز": "https://basmanews.com",
        "وكالة يد العراق الإخبارية": "https://yadiraq.com",
        "وكالة الخبر اليقين الإعلامية": "https://alkhabaryaqeen.com",
        "وكالة تراثنا نيوز": "https://turathnews.com",
        "وكالة تحديث الاخبار": "https://tahdeethnews.com",
        "وكالة تودي نيوز": "https://todaynews-iq.com",
        "وكالة القبس للأنباء الدولية": "https://alqabas.com",
        "وكالة نهج نيوز الإخبارية": "https://nahjnews.com",
        "وكالة شبكة العراق الرقمي": "https://iraqdigital.net",
        "وكالة الوسق الإخبارية": "https://alwasaq.com",
        "وكالة رادار بوست": "https://radarpost.net",
        "وكالة الاكاديمي نيوز": "https://academicnews.net",
        "وكالة النخب العراقية الإخبارية": "https://iraqielites.com",
        "وكالة الخضراء الخبرية": "https://alkhadranews.com",
        "وكالة قناة الريتاج الالكترونية": "https://alritaj.tv",
        "وكالة السداد الإخبارية": "https://alsadad.news",
        "وكالة الشبكة الالكترونية": "https://alshbaka.net",
        "وكالة شجن نيوز": "https://shajannews.com",
        "وكالة أوساط": "https://awsat-news.com",
        "وكالة صوت الديوانية": "https://diwaniyavoice.com",
        "وكالة غرفة الاخبار العراقية": "https://iraqnewsroom.com",
        "وكالة قدرة الإخبارية": "https://qudranews.com",
        "وكالة زابو ميديا": "https://zabomedia.com",
        "وكالة العدالة نيوز": "https://adalanews.com",
        "وكالة اخبار وطن": "https://watannews.net",
        "وكالة الوان الإخبارية": "https://alwannews.com",
        "وكالة الفاصلة الإخبارية": "https://alfaslanews.com",
        "وكالة مرفأ": "https://marfanews.com"
    }
    
    print("🌐 تحديث المواقع الإضافية...")
    print("=" * 50)
    
    updated_count = 0
    not_found_count = 0
    already_exists_count = 0
    
    # جلب جميع المصادر
    sources = db.get_all_sources()
    source_dict = {source[1]: source for source in sources}
    
    for source_name, website in additional_websites.items():
        if source_name in source_dict:
            source_data = source_dict[source_name]
            source_id, name, current_website, email, whatsapp = source_data
            
            if not current_website or not current_website.strip():
                success = db.update_source(
                    source_id=source_id,
                    name=name,
                    website=website,
                    email=email,
                    whatsapp=whatsapp
                )
                
                if success:
                    print(f"✅ {source_name}")
                    print(f"   🌐 {website}")
                    updated_count += 1
                else:
                    print(f"❌ فشل في تحديث: {source_name}")
            else:
                print(f"⏭️  {source_name} - يوجد موقع مسبقاً")
                already_exists_count += 1
        else:
            print(f"❓ لم يتم العثور على: {source_name}")
            not_found_count += 1
    
    print("\n" + "=" * 50)
    print("📊 ملخص العملية:")
    print(f"   ✅ تم تحديثها: {updated_count}")
    print(f"   ⏭️  موجودة مسبقاً: {already_exists_count}")
    print(f"   ❓ غير موجودة: {not_found_count}")
    
    if updated_count > 0:
        print(f"\n🎉 تم تحديث {updated_count} مصدر إضافي!")

def show_current_status():
    """عرض حالة المواقع الحالية"""
    db = DatabaseManager()
    sources = db.get_all_sources()
    
    with_websites = 0
    without_websites = 0
    
    print("\n📊 حالة المواقع الحالية:")
    print("=" * 40)
    
    for source in sources:
        source_id, name, website, email, whatsapp = source
        if website and website.strip():
            with_websites += 1
        else:
            without_websites += 1
    
    total = len(sources)
    percentage = (with_websites / total * 100) if total > 0 else 0
    
    print(f"إجمالي المصادر: {total}")
    print(f"✅ لديها مواقع: {with_websites} ({percentage:.1f}%)")
    print(f"❌ بدون مواقع: {without_websites}")
    
    if without_websites > 0:
        print(f"\n📋 المصادر بدون مواقع:")
        count = 0
        for source in sources:
            source_id, name, website, email, whatsapp = source
            if not website or not website.strip():
                count += 1
                print(f"   {count:2d}. {name}")
                if count >= 10:  # عرض أول 10 فقط
                    remaining = without_websites - 10
                    if remaining > 0:
                        print(f"   ... و {remaining} مصدر آخر")
                    break

def main():
    """الدالة الرئيسية"""
    print("🌐 تحديث المواقع الإضافية")
    print("=" * 40)
    
    print("اختر العملية:")
    print("1. تحديث المواقع الإضافية")
    print("2. عرض حالة المواقع الحالية")
    print("3. كلاهما")
    
    try:
        choice = input("\nأدخل اختيارك (1-3): ").strip()
        
        if choice == "1":
            update_additional_websites()
        elif choice == "2":
            show_current_status()
        elif choice == "3":
            update_additional_websites()
            show_current_status()
        else:
            print("اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
