# دليل المطور - برنامج إدارة المصادر الإخبارية

## 🏗️ هيكل المشروع

```
media_sources_manager/
├── main.py                 # نقطة البداية الرئيسية
├── database.py             # إدارة قاعدة البيانات
├── models.py               # نماذج البيانات
├── config.py               # إعدادات التطبيق
├── sample_data.py          # بيانات تجريبية
├── test_app.py             # اختبارات التطبيق
├── requirements.txt        # المتطلبات
├── run.bat                 # تشغيل Windows
├── run.sh                  # تشغيل Linux/Mac
├── gui/
│   ├── __init__.py
│   ├── main_window.py      # النافذة الرئيسية
│   ├── add_edit_dialog.py  # نافذة الإضافة والتعديل
│   └── styles.py           # أنماط الواجهة
├── README.md               # دليل المستخدم
└── DEVELOPER_GUIDE.md      # دليل المطور
```

## 🔧 المكونات الرئيسية

### 1. قاعدة البيانات (database.py)
- **DatabaseManager**: فئة إدارة قاعدة البيانات
- **الوظائف الرئيسية**:
  - `init_database()`: إنشاء قاعدة البيانات والجداول
  - `add_source()`: إضافة مصدر جديد
  - `get_all_sources()`: جلب جميع المصادر
  - `update_source()`: تحديث مصدر موجود
  - `delete_source()`: حذف مصدر
  - `search_sources()`: البحث في المصادر

### 2. نماذج البيانات (models.py)
- **MediaSource**: نموذج بيانات المصدر الإخباري
- **الخصائص**:
  - `id`: المعرف الفريد
  - `name`: اسم المصدر (مطلوب)
  - `website`: الموقع الرسمي
  - `email`: الإيميل الرسمي
  - `whatsapp`: رقم واتساب

### 3. الواجهة الرسومية (gui/)
- **MainWindow**: النافذة الرئيسية
- **AddEditDialog**: نافذة الإضافة والتعديل
- **AppStyles**: أنماط الواجهة

## 🎨 تخصيص الواجهة

### الألوان (gui/styles.py)
```python
PRIMARY_COLOR = "#2E86AB"      # أزرق أساسي
SECONDARY_COLOR = "#A23B72"    # وردي ثانوي
SUCCESS_COLOR = "#F18F01"      # برتقالي للنجاح
DANGER_COLOR = "#C73E1D"       # أحمر للخطر
```

### الخطوط
```python
TITLE_FONT = ("Arial", 16, "bold")
HEADER_FONT = ("Arial", 12, "bold")
NORMAL_FONT = ("Arial", 10)
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
python test_app.py
```

### أنواع الاختبارات
- **TestDatabaseManager**: اختبارات قاعدة البيانات
- **TestMediaSource**: اختبارات نموذج البيانات

## 📊 قاعدة البيانات

### جدول media_sources
```sql
CREATE TABLE media_sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    website TEXT,
    email TEXT,
    whatsapp TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 إضافة ميزات جديدة

### 1. إضافة حقل جديد
1. تحديث جدول قاعدة البيانات في `database.py`
2. تحديث نموذج `MediaSource` في `models.py`
3. تحديث واجهة الإضافة/التعديل في `gui/add_edit_dialog.py`
4. تحديث عرض الجدول في `gui/main_window.py`

### 2. إضافة نافذة جديدة
1. إنشاء ملف جديد في مجلد `gui/`
2. استيراد الأنماط من `gui/styles.py`
3. ربط النافذة بالنافذة الرئيسية

### 3. إضافة وظيفة تصدير
```python
def export_to_csv(self, filename):
    """تصدير البيانات إلى CSV"""
    import csv
    sources = self.db.get_all_sources()
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['اسم المصدر', 'الموقع', 'الإيميل', 'واتساب'])
        for source in sources:
            writer.writerow(source[1:])  # تجاهل ID
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في قاعدة البيانات**: تحقق من صلاحيات الكتابة
2. **خطأ في الواجهة**: تحقق من تثبيت tkinter
3. **خطأ في الترميز**: تأكد من دعم UTF-8

### سجلات الأخطاء
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
```

## 📈 تحسينات مستقبلية

### الأولوية العالية
- [ ] إضافة تصدير البيانات (CSV, Excel)
- [ ] إضافة استيراد البيانات
- [ ] إضافة نسخ احتياطي للبيانات
- [ ] إضافة فلترة متقدمة

### الأولوية المتوسطة
- [ ] إضافة أيقونات للتطبيق
- [ ] إضافة اختصارات لوحة المفاتيح
- [ ] إضافة إعدادات المستخدم
- [ ] إضافة طباعة التقارير

### الأولوية المنخفضة
- [ ] إضافة قاعدة بيانات خارجية (MySQL/PostgreSQL)
- [ ] إضافة واجهة ويب
- [ ] إضافة API للتكامل
- [ ] إضافة إشعارات

## 🤝 المساهمة

### معايير الكود
- استخدام أسماء متغيرات واضحة
- إضافة تعليقات للوظائف المعقدة
- اتباع PEP 8 لتنسيق Python
- كتابة اختبارات للميزات الجديدة

### عملية المراجعة
1. إنشاء فرع جديد للميزة
2. كتابة الكود والاختبارات
3. تشغيل جميع الاختبارات
4. إنشاء طلب دمج

---
**تم إعداده بواسطة مساعد الذكي - 2025**
