import sqlite3
import os
from typing import List, Optional, Tuple

class DatabaseManager:
    """مدير قاعدة البيانات لإدارة المصادر الإخبارية"""
    
    def __init__(self, db_path: str = "media_sources.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS media_sources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    website TEXT,
                    email TEXT,
                    whatsapp TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
    
    def add_source(self, name: str, website: str = "", email: str = "", whatsapp: str = "") -> bool:
        """إضافة مصدر جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO media_sources (name, website, email, whatsapp)
                    VALUES (?, ?, ?, ?)
                ''', (name, website, email, whatsapp))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # اسم المصدر موجود مسبقاً
        except Exception as e:
            print(f"خطأ في إضافة المصدر: {e}")
            return False
    
    def get_all_sources(self) -> List[Tuple]:
        """جلب جميع المصادر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, name, website, email, whatsapp 
                    FROM media_sources 
                    ORDER BY name
                ''')
                return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في جلب المصادر: {e}")
            return []
    
    def get_source_by_id(self, source_id: int) -> Optional[Tuple]:
        """جلب مصدر بواسطة المعرف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, name, website, email, whatsapp 
                    FROM media_sources 
                    WHERE id = ?
                ''', (source_id,))
                return cursor.fetchone()
        except Exception as e:
            print(f"خطأ في جلب المصدر: {e}")
            return None
    
    def update_source(self, source_id: int, name: str, website: str = "", 
                     email: str = "", whatsapp: str = "") -> bool:
        """تحديث مصدر موجود"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE media_sources 
                    SET name = ?, website = ?, email = ?, whatsapp = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (name, website, email, whatsapp, source_id))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.IntegrityError:
            return False  # اسم المصدر موجود مسبقاً
        except Exception as e:
            print(f"خطأ في تحديث المصدر: {e}")
            return False
    
    def delete_source(self, source_id: int) -> bool:
        """حذف مصدر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM media_sources WHERE id = ?', (source_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في حذف المصدر: {e}")
            return False
    
    def search_sources(self, search_term: str) -> List[Tuple]:
        """البحث في المصادر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                search_pattern = f"%{search_term}%"
                cursor.execute('''
                    SELECT id, name, website, email, whatsapp 
                    FROM media_sources 
                    WHERE name LIKE ? OR website LIKE ? OR email LIKE ?
                    ORDER BY name
                ''', (search_pattern, search_pattern, search_pattern))
                return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []
