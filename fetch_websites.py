#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البحث عن المواقع الرسمية للمصادر الإخبارية العراقية وإدخالها إلى قاعدة البيانات
"""

import time
import re
from database import DatabaseManager
from web_search import web_search

def clean_source_name(name):
    """تنظيف اسم المصدر للبحث"""
    # إزالة الكلمات الشائعة التي قد تؤثر على البحث
    common_words = ['وكالة', 'قناة', 'موقع', 'مكتب', 'شركة', 'مركز', 'منصة', 'شبكة', 'إذاعة', 'ديوان']
    
    # تنظيف الاسم
    cleaned = name.strip()
    
    # إزالة الأقواس ومحتوياتها
    cleaned = re.sub(r'\([^)]*\)', '', cleaned).strip()
    
    # إضافة كلمات البحث المناسبة
    search_terms = [cleaned, f"{cleaned} العراق", f"{cleaned} iraq", f"{cleaned} news"]
    
    return search_terms

def validate_website_match(source_name, website_url, search_results):
    """التحقق من مطابقة الموقع لاسم المصدر"""
    if not website_url:
        return False, "لا يوجد موقع"
    
    # تنظيف اسم المصدر
    source_clean = source_name.lower().replace('وكالة', '').replace('قناة', '').replace('موقع', '').strip()
    
    # استخراج اسم النطاق
    domain_match = re.search(r'https?://(?:www\.)?([^/]+)', website_url)
    if not domain_match:
        return False, "رابط غير صحيح"
    
    domain = domain_match.group(1).lower()
    
    # البحث عن كلمات مفتاحية من اسم المصدر في النطاق
    source_words = source_clean.split()
    domain_words = domain.replace('.', ' ').replace('-', ' ').split()
    
    # التحقق من وجود تطابق
    matches = 0
    for word in source_words:
        if len(word) > 2:  # تجاهل الكلمات القصيرة
            for domain_word in domain_words:
                if word in domain_word or domain_word in word:
                    matches += 1
                    break
    
    # التحقق من محتوى نتائج البحث
    content_match = False
    if search_results:
        for result in search_results[:3]:  # فحص أول 3 نتائج
            title = result.get('title', '').lower()
            snippet = result.get('snippet', '').lower()
            if source_clean in title or source_clean in snippet:
                content_match = True
                break
    
    # قرار المطابقة
    if matches > 0 or content_match:
        confidence = "عالية" if matches > 1 or content_match else "متوسطة"
        return True, f"مطابقة {confidence} ({matches} كلمات متطابقة)"
    
    return False, "لا توجد مطابقة واضحة"

def search_and_update_websites(batch_size=10, start_index=0):
    """البحث عن المواقع وتحديث قاعدة البيانات"""
    db = DatabaseManager()

    # جلب جميع المصادر
    sources = db.get_all_sources()

    print("🔍 البحث عن المواقع الرسمية للمصادر الإخبارية العراقية...")
    print(f"📊 معالجة {batch_size} مصادر بدءاً من المصدر رقم {start_index + 1}")
    print("=" * 70)

    updated_count = 0
    skipped_count = 0
    failed_count = 0

    # تحديد النطاق للمعالجة
    end_index = min(start_index + batch_size, len(sources))
    batch_sources = sources[start_index:end_index]

    for i, source_data in enumerate(batch_sources, start_index + 1):
        source_id, name, current_website, email, whatsapp = source_data

        # تخطي المصادر التي لديها مواقع بالفعل
        if current_website and current_website.strip():
            print(f"⏭️  {i:3d}. {name} - يوجد موقع مسبقاً: {current_website}")
            skipped_count += 1
            continue

        print(f"\n🔍 {i:3d}. البحث عن: {name}")

        try:
            # تنظيف اسم المصدر للبحث
            search_terms = clean_source_name(name)

            best_website = None
            best_confidence = None
            best_results = None

            # البحث باستخدام مصطلحات مختلفة
            for search_term in search_terms[:2]:  # استخدام أول مصطلحين فقط
                try:
                    print(f"   🔎 البحث عن: {search_term}")
                    results = web_search(search_term, num_results=5)

                    if results:
                        # فحص النتائج
                        for result in results:
                            url = result.get('url', '')
                            title = result.get('title', '')

                            if url and ('http' in url):
                                # التحقق من المطابقة
                                is_match, confidence = validate_website_match(name, url, results)

                                if is_match:
                                    print(f"   ✅ وجد موقع محتمل: {url}")
                                    print(f"      العنوان: {title}")
                                    print(f"      الثقة: {confidence}")

                                    if not best_website or "عالية" in confidence:
                                        best_website = url
                                        best_confidence = confidence
                                        best_results = results

                                        if "عالية" in confidence:
                                            break

                    # توقف قصير بين البحثات
                    time.sleep(1)

                    if best_website and "عالية" in str(best_confidence):
                        break

                except Exception as e:
                    print(f"   ❌ خطأ في البحث: {e}")
                    continue

            # تحديث قاعدة البيانات إذا وُجد موقع مناسب
            if best_website:
                print(f"\n   📋 ملخص النتيجة:")
                print(f"   🌐 الموقع المقترح: {best_website}")
                print(f"   📊 مستوى الثقة: {best_confidence}")

                # عرض عينة من نتائج البحث للتأكيد
                if best_results:
                    print(f"   📰 عينة من النتائج:")
                    for j, result in enumerate(best_results[:2], 1):
                        print(f"      {j}. {result.get('title', 'بدون عنوان')}")

                # تحديث قاعدة البيانات
                success = db.update_source(
                    source_id=source_id,
                    name=name,
                    website=best_website,
                    email=email,
                    whatsapp=whatsapp
                )

                if success:
                    print(f"   ✅ تم تحديث الموقع بنجاح!")
                    updated_count += 1
                else:
                    print(f"   ❌ فشل في تحديث قاعدة البيانات")
                    failed_count += 1
            else:
                print(f"   ❌ لم يتم العثور على موقع مناسب")
                failed_count += 1

            # توقف بين المصادر لتجنب الحظر
            time.sleep(2)

        except Exception as e:
            print(f"   ❌ خطأ عام: {e}")
            failed_count += 1
            continue

    print("\n" + "=" * 70)
    print("📊 ملخص العملية:")
    print(f"   المصادر المعالجة: {len(batch_sources)}")
    print(f"   ✅ تم تحديثها: {updated_count}")
    print(f"   ⏭️  تم تخطيها (لديها مواقع): {skipped_count}")
    print(f"   ❌ فشلت: {failed_count}")

    if updated_count > 0:
        print(f"\n🎉 تم تحديث {updated_count} مصدر بمواقعهم الرسمية!")

    # اقتراح المتابعة
    if end_index < len(sources):
        remaining = len(sources) - end_index
        print(f"\n📌 يتبقى {remaining} مصدر للمعالجة")
        print(f"💡 لمتابعة المعالجة، شغل البرنامج مع start_index={end_index}")
    else:
        print("\n🏁 تم الانتهاء من معالجة جميع المصادر!")
        print("يمكنك الآن تشغيل البرنامج الرئيسي لرؤية النتائج")

    return updated_count, skipped_count, failed_count

def main():
    """الدالة الرئيسية"""
    print("🌐 برنامج البحث عن المواقع الرسمية للمصادر الإخبارية")
    print("=" * 60)

    # عرض الخيارات
    print("اختر طريقة المعالجة:")
    print("1. معالجة 5 مصادر فقط (للاختبار)")
    print("2. معالجة 10 مصادر")
    print("3. معالجة 20 مصدر")
    print("4. معالجة جميع المصادر (قد يستغرق وقت طويل)")
    print("5. متابعة من نقطة معينة")

    try:
        choice = input("\nأدخل اختيارك (1-5): ").strip()

        if choice == "1":
            search_and_update_websites(batch_size=5, start_index=0)
        elif choice == "2":
            search_and_update_websites(batch_size=10, start_index=0)
        elif choice == "3":
            search_and_update_websites(batch_size=20, start_index=0)
        elif choice == "4":
            # معالجة جميع المصادر على دفعات
            db = DatabaseManager()
            sources = db.get_all_sources()
            total_sources = len(sources)

            print(f"سيتم معالجة {total_sources} مصدر على دفعات من 10 مصادر")
            confirm = input("هل تريد المتابعة؟ (y/n): ").strip().lower()

            if confirm in ['y', 'yes', 'نعم']:
                batch_size = 10
                for start_idx in range(0, total_sources, batch_size):
                    print(f"\n{'='*50}")
                    print(f"معالجة الدفعة: {start_idx//batch_size + 1}")
                    search_and_update_websites(batch_size=batch_size, start_index=start_idx)

                    # توقف بين الدفعات
                    if start_idx + batch_size < total_sources:
                        print("\nتوقف لمدة 10 ثوان بين الدفعات...")
                        time.sleep(10)
            else:
                print("تم إلغاء العملية")

        elif choice == "5":
            start_index = int(input("أدخل رقم المصدر للبدء منه (بدءاً من 0): "))
            batch_size = int(input("أدخل عدد المصادر للمعالجة: "))
            search_and_update_websites(batch_size=batch_size, start_index=start_index)
        else:
            print("اختيار غير صحيح")

    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
