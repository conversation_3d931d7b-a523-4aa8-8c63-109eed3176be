# برنامج إدارة المصادر الإخبارية

## 📋 نظرة عامة
برنامج مكتبي احترافي لإدارة المصادر الإخبارية والمؤسسات الإعلامية، مطور بلغة Python مع واجهة مستخدم حديثة وسهلة الاستخدام.

## ✨ الميزات الرئيسية
- **إضافة مصادر جديدة**: إضافة مصادر إخبارية جديدة بسهولة
- **تعديل المصادر**: تحديث معلومات المصادر الموجودة
- **حذف المصادر**: إزالة المصادر غير المرغوب فيها
- **عرض شامل**: جدول تفاعلي لعرض جميع المصادر
- **البحث المتقدم**: البحث السريع في المصادر
- **واجهة احترافية**: تصميم حديث وسهل الاستخدام
- **قاعدة بيانات محلية**: حفظ آمن للبيانات باستخدام SQLite

## 📊 حقول البيانات
- **اسم المصدر** (مطلوب)
- **الموقع الرسمي**
- **الإيميل الرسمي**
- **رقم هاتف واتساب**

## 🛠️ المتطلبات
- Python 3.6 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

## 📦 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd media_sources_manager
```

### 2. تشغيل البرنامج
```bash
python main.py
```

## 🎯 كيفية الاستخدام

### إضافة مصدر جديد
1. انقر على زر "➕ إضافة مصدر"
2. املأ البيانات المطلوبة (اسم المصدر مطلوب)
3. انقر على "حفظ"

### تعديل مصدر موجود
1. اختر المصدر من الجدول
2. انقر على زر "✏️ تعديل" أو انقر نقرة مزدوجة على المصدر
3. عدّل البيانات المطلوبة
4. انقر على "تحديث"

### حذف مصدر
1. اختر المصدر من الجدول
2. انقر على زر "🗑️ حذف" أو اضغط مفتاح Delete
3. أكد عملية الحذف

### البحث في المصادر
- استخدم حقل البحث في أعلى يمين الشاشة
- البحث يشمل اسم المصدر والموقع والإيميل

## 🗂️ هيكل المشروع
```
media_sources_manager/
├── main.py                 # نقطة البداية الرئيسية
├── database.py             # إدارة قاعدة البيانات
├── models.py               # نماذج البيانات
├── requirements.txt        # المتطلبات
├── gui/
│   ├── __init__.py
│   ├── main_window.py      # النافذة الرئيسية
│   ├── add_edit_dialog.py  # نافذة الإضافة والتعديل
│   └── styles.py           # أنماط الواجهة
└── README.md               # دليل الاستخدام
```

## 🎨 لقطات الشاشة
- واجهة رئيسية احترافية مع جدول تفاعلي
- نوافذ إضافة وتعديل سهلة الاستخدام
- تصميم عصري بألوان متناسقة

## 🔧 التخصيص
يمكن تخصيص ألوان وأنماط الواجهة من خلال ملف `gui/styles.py`

## 📝 ملاحظات
- يتم حفظ البيانات في ملف `media_sources.db` في نفس مجلد البرنامج
- البرنامج يدعم اللغة العربية بالكامل
- جميع العمليات محمية بالتحقق من صحة البيانات

## 🐛 الإبلاغ عن المشاكل
في حالة مواجهة أي مشاكل، يرجى التأكد من:
- تثبيت Python بشكل صحيح
- توفر جميع المتطلبات
- صلاحيات الكتابة في مجلد البرنامج

## 📄 الترخيص
هذا البرنامج مجاني للاستخدام الشخصي والتجاري.

---
**تم تطويره بواسطة مساعد الذكي - 2025**
