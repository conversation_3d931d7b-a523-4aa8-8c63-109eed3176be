import tkinter as tk
from tkinter import ttk

class AppStyles:
    """أنماط التطبيق الاحترافية"""
    
    # الألوان
    PRIMARY_COLOR = "#2E86AB"      # أزرق أساسي
    SECONDARY_COLOR = "#A23B72"    # وردي ثانوي
    SUCCESS_COLOR = "#F18F01"      # برتقالي للنجاح
    DANGER_COLOR = "#C73E1D"       # أحمر للخطر
    BACKGROUND_COLOR = "#F5F5F5"   # خلفية رمادية فاتحة
    WHITE_COLOR = "#FFFFFF"        # أبيض
    DARK_COLOR = "#2C3E50"         # رمادي داكن
    LIGHT_GRAY = "#ECF0F1"         # رمادي فاتح
    
    # الخطوط
    TITLE_FONT = ("Arial", 16, "bold")
    HEADER_FONT = ("Arial", 12, "bold")
    NORMAL_FONT = ("Arial", 10)
    BUTTON_FONT = ("Arial", 10, "bold")
    
    @staticmethod
    def configure_styles():
        """تكوين أنماط ttk"""
        style = ttk.Style()
        
        # تكوين النمط العام
        style.theme_use('clam')
        
        # نمط الأزرار
        style.configure(
            "Primary.TButton",
            background=AppStyles.PRIMARY_COLOR,
            foreground="white",
            font=AppStyles.BUTTON_FONT,
            padding=(10, 5)
        )
        
        style.map(
            "Primary.TButton",
            background=[('active', '#1F5F7A'), ('pressed', '#1A4F66')]
        )
        
        style.configure(
            "Success.TButton",
            background=AppStyles.SUCCESS_COLOR,
            foreground="white",
            font=AppStyles.BUTTON_FONT,
            padding=(10, 5)
        )
        
        style.configure(
            "Danger.TButton",
            background=AppStyles.DANGER_COLOR,
            foreground="white",
            font=AppStyles.BUTTON_FONT,
            padding=(10, 5)
        )
        
        # نمط الجدول
        style.configure(
            "Custom.Treeview",
            background="white",
            foreground=AppStyles.DARK_COLOR,
            rowheight=25,
            fieldbackground="white",
            font=AppStyles.NORMAL_FONT
        )
        
        style.configure(
            "Custom.Treeview.Heading",
            background=AppStyles.PRIMARY_COLOR,
            foreground="white",
            font=AppStyles.HEADER_FONT,
            relief="flat"
        )
        
        style.map(
            "Custom.Treeview",
            background=[('selected', AppStyles.PRIMARY_COLOR)],
            foreground=[('selected', 'white')]
        )
        
        # نمط الإطارات
        style.configure(
            "Card.TFrame",
            background="white",
            relief="solid",
            borderwidth=1
        )
        
        # نمط التسميات
        style.configure(
            "Title.TLabel",
            background="white",
            foreground=AppStyles.DARK_COLOR,
            font=AppStyles.TITLE_FONT
        )
        
        style.configure(
            "Header.TLabel",
            background="white",
            foreground=AppStyles.DARK_COLOR,
            font=AppStyles.HEADER_FONT
        )
        
        # نمط حقول الإدخال
        style.configure(
            "Custom.TEntry",
            fieldbackground="white",
            borderwidth=1,
            relief="solid",
            font=AppStyles.NORMAL_FONT
        )
        
        return style
