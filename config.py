#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين لبرنامج إدارة المصادر الإخبارية
"""

import os

class Config:
    """إعدادات التطبيق"""
    
    # إعدادات قاعدة البيانات
    DATABASE_NAME = "media_sources.db"
    DATABASE_PATH = os.path.join(os.path.dirname(__file__), DATABASE_NAME)
    
    # إعدادات النافذة الرئيسية
    WINDOW_TITLE = "إدارة المصادر الإخبارية"
    WINDOW_WIDTH = 1000
    WINDOW_HEIGHT = 700
    WINDOW_MIN_WIDTH = 800
    WINDOW_MIN_HEIGHT = 600
    
    # إعدادات الجدول
    TABLE_ROW_HEIGHT = 25
    TABLE_COLUMNS = {
        "name": {"text": "اسم المصدر", "width": 200, "minwidth": 150},
        "website": {"text": "الموقع الرسمي", "width": 250, "minwidth": 200},
        "email": {"text": "الإيميل الرسمي", "width": 200, "minwidth": 150},
        "whatsapp": {"text": "رقم واتساب", "width": 150, "minwidth": 120}
    }
    
    # إعدادات النوافذ المنبثقة
    DIALOG_WIDTH = 500
    DIALOG_HEIGHT = 400
    
    # رسائل التطبيق
    MESSAGES = {
        "add_success": "تم إضافة المصدر بنجاح!",
        "add_error": "فشل في إضافة المصدر. قد يكون الاسم موجود مسبقاً.",
        "update_success": "تم تحديث المصدر بنجاح!",
        "update_error": "فشل في تحديث المصدر",
        "delete_success": "تم حذف المصدر بنجاح!",
        "delete_error": "فشل في حذف المصدر",
        "delete_confirm": "هل أنت متأكد من حذف هذا المصدر؟\nلا يمكن التراجع عن هذا الإجراء.",
        "no_selection": "يرجى اختيار مصدر أولاً",
        "name_required": "اسم المصدر مطلوب!",
        "invalid_email": "تنسيق الإيميل غير صحيح!",
        "database_error": "حدث خطأ في قاعدة البيانات",
        "ready": "جاهز",
        "data_refreshed": "تم تحديث البيانات بنجاح",
        "source_added": "تم إضافة مصدر جديد",
        "source_updated": "تم تحديث المصدر",
        "source_deleted": "تم حذف المصدر"
    }
    
    # إعدادات البحث
    SEARCH_PLACEHOLDER = "البحث في المصادر..."
    
    # إعدادات التصدير (للمستقبل)
    EXPORT_FORMATS = ["CSV", "Excel", "JSON"]
    
    @classmethod
    def get_database_path(cls):
        """الحصول على مسار قاعدة البيانات"""
        return cls.DATABASE_PATH
    
    @classmethod
    def get_window_geometry(cls):
        """الحصول على أبعاد النافذة"""
        return f"{cls.WINDOW_WIDTH}x{cls.WINDOW_HEIGHT}"
    
    @classmethod
    def get_dialog_geometry(cls):
        """الحصول على أبعاد النوافذ المنبثقة"""
        return f"{cls.DIALOG_WIDTH}x{cls.DIALOG_HEIGHT}"
