#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء تقرير شامل عن المصادر الإخبارية ومواقعها
"""

from database import DatabaseManager
import datetime

def generate_detailed_report():
    """إنشاء تقرير مفصل"""
    db = DatabaseManager()
    sources = db.get_all_sources()
    
    # إحصائيات
    total_sources = len(sources)
    sources_with_websites = sum(1 for s in sources if s[2] and s[2].strip())
    sources_without_websites = total_sources - sources_with_websites
    percentage_with_websites = (sources_with_websites / total_sources * 100) if total_sources > 0 else 0
    
    # إنشاء التقرير
    report = []
    report.append("=" * 80)
    report.append("📊 تقرير شامل عن المصادر الإخبارية العراقية")
    report.append("=" * 80)
    report.append(f"📅 تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # الإحصائيات العامة
    report.append("📈 الإحصائيات العامة:")
    report.append("-" * 30)
    report.append(f"إجمالي المصادر: {total_sources}")
    report.append(f"✅ لديها مواقع: {sources_with_websites} ({percentage_with_websites:.1f}%)")
    report.append(f"❌ بدون مواقع: {sources_without_websites}")
    report.append("")
    
    # المصادر التي لديها مواقع
    report.append("🌐 المصادر التي لديها مواقع رسمية:")
    report.append("-" * 50)
    count = 0
    for source in sources:
        source_id, name, website, email, whatsapp = source
        if website and website.strip():
            count += 1
            report.append(f"{count:3d}. {name}")
            report.append(f"     🌐 {website}")
            if email and email.strip():
                report.append(f"     📧 {email}")
            if whatsapp and whatsapp.strip():
                report.append(f"     📱 {whatsapp}")
            report.append("")
    
    # المصادر بدون مواقع
    report.append("\n❌ المصادر بدون مواقع:")
    report.append("-" * 30)
    count = 0
    for source in sources:
        source_id, name, website, email, whatsapp = source
        if not website or not website.strip():
            count += 1
            report.append(f"{count:3d}. {name}")
    
    report.append("")
    report.append("=" * 80)
    report.append("📝 ملاحظات:")
    report.append("- يمكن استخدام البرنامج الرئيسي لإضافة المزيد من التفاصيل")
    report.append("- يمكن البحث عن المواقع المتبقية باستخدام fetch_websites.py")
    report.append("- يمكن إضافة الإيميلات وأرقام الواتساب يدوياً من خلال البرنامج")
    report.append("=" * 80)
    
    return "\n".join(report)

def save_report_to_file():
    """حفظ التقرير في ملف"""
    report_content = generate_detailed_report()
    
    # اسم الملف مع التاريخ
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"media_sources_report_{timestamp}.txt"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ تم حفظ التقرير في: {filename}")
        return filename
    except Exception as e:
        print(f"❌ خطأ في حفظ التقرير: {e}")
        return None

def print_summary():
    """طباعة ملخص سريع"""
    db = DatabaseManager()
    sources = db.get_all_sources()
    
    total = len(sources)
    with_websites = sum(1 for s in sources if s[2] and s[2].strip())
    without_websites = total - with_websites
    percentage = (with_websites / total * 100) if total > 0 else 0
    
    print("📊 ملخص سريع:")
    print("=" * 40)
    print(f"إجمالي المصادر: {total}")
    print(f"✅ لديها مواقع: {with_websites} ({percentage:.1f}%)")
    print(f"❌ بدون مواقع: {without_websites}")
    print("")
    
    # أمثلة على المصادر المهمة
    important_sources = [
        "شبكة الجزيرة الإعلامية",
        "وكالة السومرية نيوز", 
        "قناة العربية الحدث",
        "وكالة كركوك نيوز",
        "وكالة المسلة"
    ]
    
    print("🌟 أمثلة على المصادر المهمة:")
    print("-" * 35)
    
    source_dict = {s[1]: s for s in sources}
    for source_name in important_sources:
        if source_name in source_dict:
            source = source_dict[source_name]
            website = source[2] if source[2] and source[2].strip() else "❌ غير متوفر"
            print(f"• {source_name}")
            print(f"  🌐 {website}")
            print()

def export_to_csv():
    """تصدير البيانات إلى CSV"""
    db = DatabaseManager()
    sources = db.get_all_sources()
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"media_sources_{timestamp}.csv"
    
    try:
        import csv
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # كتابة الهيدر
            writer.writerow(['الرقم', 'اسم المصدر', 'الموقع الرسمي', 'الإيميل', 'رقم الواتساب'])
            
            # كتابة البيانات
            for i, source in enumerate(sources, 1):
                source_id, name, website, email, whatsapp = source
                writer.writerow([i, name, website or '', email or '', whatsapp or ''])
        
        print(f"✅ تم تصدير البيانات إلى: {filename}")
        return filename
    except Exception as e:
        print(f"❌ خطأ في التصدير: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("📊 إنشاء تقارير المصادر الإخبارية")
    print("=" * 45)
    
    print("اختر نوع التقرير:")
    print("1. ملخص سريع")
    print("2. تقرير مفصل (عرض)")
    print("3. حفظ تقرير مفصل في ملف")
    print("4. تصدير إلى CSV")
    print("5. جميع ما سبق")
    
    try:
        choice = input("\nأدخل اختيارك (1-5): ").strip()
        
        if choice == "1":
            print_summary()
        elif choice == "2":
            report = generate_detailed_report()
            print(report)
        elif choice == "3":
            save_report_to_file()
        elif choice == "4":
            export_to_csv()
        elif choice == "5":
            print("\n1️⃣ الملخص السريع:")
            print_summary()
            
            print("\n2️⃣ حفظ التقرير المفصل:")
            save_report_to_file()
            
            print("\n3️⃣ تصدير إلى CSV:")
            export_to_csv()
            
            print("\n✅ تم إنشاء جميع التقارير!")
        else:
            print("اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
