#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدخال المصادر الإخبارية العراقية إلى قاعدة البيانات
"""

from database import DatabaseManager

def add_iraqi_media_sources():
    """إضافة المصادر الإخبارية العراقية"""
    db = DatabaseManager()
    
    # قائمة المصادر الإخبارية العراقية
    iraqi_sources = [
        "قناة نهضة العراق الالكترونية",
        "وكالة كركوك نيوز",
        "وكالة المسلة",
        "وكالة الاعلام الدولي",
        "قناة العربية نيوز الدولية الالكترونية",
        "وكالة اليوم الإخبارية",
        "وكالة موسوعة الرافدين",
        "وكالة البوابة الإخبارية",
        "وكالة شبكة صقر للإعلام الأمني",
        "وكالة نواة الإخبارية",
        "وكالة سنا الإخبارية",
        "وكالة بغداد اليوم الإخبارية",
        "قناة النهرين الإخبارية الرقمية",
        "وكالة amb",
        "وكالة شمس نيوز مكتب ذي قار",
        "وكالة النسور للإعلام الحربي",
        "وكالة كواليس العراق",
        "وكالة المركز الوطني للأنباء",
        "وكالة المنصة الإعلامية dot راس سطر",
        "وكالة نقطة الإخبارية",
        "وكالة العالمية الان",
        "وكالة ايراق الإخبارية",
        "وكالة الغد برس",
        "وكالة ميل الإخبارية",
        "موقع قناة البصرة 365",
        "وكالة مركز الابداع الإعلامي",
        "وكالة الرصافي نيوز",
        "وكالة الرصد برس",
        "وكالة المسرى",
        "وكالة هنا العراق الإخبارية الدولية",
        "وكالة بصمة نيوز",
        "موقع قناة النهرين الفضائية",
        "وكالة يد العراق الإخبارية",
        "وكالة كربلاء 24 الإخبارية",
        "وكالة الخبر اليقين الإعلامية",
        "وكالة الخبر برس واخ",
        "وكالة مجلة اور الثقافية",
        "موقع قناة المدار الإخبارية الرقمية",
        "وكالة تراثنا نيوز",
        "وكالة تحديث الاخبار",
        "وكالة شبكة دعم النزاهة الإعلامية",
        "وكالة البينة الإخبارية",
        "وكالة تودي نيوز",
        "وكالة ايشان",
        "وكالة القبس للأنباء الدولية",
        "وكالة نهج نيوز الإخبارية",
        "وكالة شبكة العراق الرقمي",
        "منصة ريادي",
        "وكالة puk media.iq",
        "موقع عراق an الإخبارية (منص وطن)",
        "وكالة الوسق الإخبارية",
        "صدى الاحداث",
        "موقع بصراوي",
        "مركز البصرة الاخباري",
        "وكالة كلدانايا",
        "وكالة اخر الاخبار lan",
        "وكالة رادار بوست",
        "وكالة الاكاديمي نيوز",
        "وكالة بصرياثا الإعلامية",
        "وكالة النخب العراقية الإخبارية",
        "وكالة الخضراء الخبرية",
        "وكالة السومرية نيوز",
        "وكالة الخبر العراقي",
        "وكالة قناة الريتاج الالكترونية",
        "وكالة السداد الإخبارية",
        "وكالة الشبكة الالكترونية",
        "وكالة العراق الان نيوز",
        "وكالة شجن نيوز",
        "وكالة أوساط",
        "وكالة صوت الديوانية",
        "وكالة 5 mede",
        "وكالة غرفة الاخبار العراقية",
        "موقع قناة am news الالكترونية",
        "وكالة قدرة الإخبارية",
        "موقع وكالة g-class الالكترونية",
        "شبكة يوميات الجنوب",
        "موقع مجلة المبدعون",
        "وكالة زابو ميديا",
        "وكالة العدالة نيوز",
        "وكالة اخبار وطن",
        "وكالة الوان الإخبارية",
        "منصة محتوى هادف",
        "وكالة الفاصلة الإخبارية",
        "المنصة ميديا",
        "إذاعة الحرية",
        "إذاعة ارض العراق",
        "إذاعة الاتجاه",
        "إذاعة التلفزيون العربي",
        "مكتب التلفزيون الألماني ard",
        "مكتب قناة ika الإخبارية",
        "شركة البيت الإعلامي للإنتاج الفني",
        "قناة المهدي",
        "ديوان الوقف الشيعي-العتبة العباسية-مركز الكفيل",
        "مكتب شركة زهو العراق للخدمات الإعلامية",
        "مكتب كركوك بلاص",
        "قناة الفلوجة الفضائية",
        "قناة العربية الحدث",
        "شبكة التلفزيون العربي",
        "شركة البوابة العالمية للإنتاج و التسويق الفني",
        "التلفزيون الصيني المركزي cctv",
        "قناة الشرق الفضائية",
        "وكالة مرفأ",
        "التلفزيون الألماني zdf",
        "صحيفة نفط العراق",
        "قناة روسيا اليوم الفضائية",
        "قناة mbc عراق الفضائية",
        "شبكة الجزيرة الإعلامية",
        "قناة صوت العقيلة الفضائية",
        "ذهب الأسود الاعلامي"
    ]
    
    print("🇮🇶 إدخال المصادر الإخبارية العراقية...")
    print("=" * 50)
    
    success_count = 0
    duplicate_count = 0
    error_count = 0
    
    for i, source_name in enumerate(iraqi_sources, 1):
        try:
            success = db.add_source(name=source_name.strip())
            
            if success:
                print(f"✅ {i:3d}. {source_name}")
                success_count += 1
            else:
                print(f"⚠️  {i:3d}. {source_name} (موجود مسبقاً)")
                duplicate_count += 1
                
        except Exception as e:
            print(f"❌ {i:3d}. {source_name} - خطأ: {e}")
            error_count += 1
    
    print("\n" + "=" * 50)
    print("📊 ملخص العملية:")
    print(f"   إجمالي المصادر: {len(iraqi_sources)}")
    print(f"   ✅ تم إضافتها بنجاح: {success_count}")
    print(f"   ⚠️  موجودة مسبقاً: {duplicate_count}")
    print(f"   ❌ أخطاء: {error_count}")
    
    if success_count > 0:
        print(f"\n🎉 تم إضافة {success_count} مصدر إخباري عراقي جديد!")
        print("يمكنك الآن تشغيل البرنامج الرئيسي لرؤية جميع المصادر")
    
    return success_count

if __name__ == "__main__":
    add_iraqi_media_sources()
