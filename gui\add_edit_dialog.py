import tkinter as tk
from tkinter import ttk, messagebox
from models import MediaSource
from gui.styles import AppStyles

class AddEditDialog:
    """نافذة إضافة وتعديل المصادر الإخبارية"""
    
    def __init__(self, parent, source=None, title="إضافة مصدر جديد"):
        self.parent = parent
        self.source = source
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تعبئة البيانات إذا كان في وضع التعديل
        if self.source:
            self.populate_fields()
        
        # التركيز على أول حقل
        self.name_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, style="Card.TFrame", padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_text = "تعديل المصدر" if self.source else "إضافة مصدر جديد"
        title_label = ttk.Label(main_frame, text=title_text, style="Title.TLabel")
        title_label.pack(pady=(0, 20))
        
        # إطار الحقول
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # حقل اسم المصدر
        ttk.Label(fields_frame, text="اسم المصدر *:", style="Header.TLabel").pack(anchor=tk.W, pady=(0, 5))
        self.name_entry = ttk.Entry(fields_frame, style="Custom.TEntry", font=AppStyles.NORMAL_FONT)
        self.name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل الموقع الرسمي
        ttk.Label(fields_frame, text="الموقع الرسمي:", style="Header.TLabel").pack(anchor=tk.W, pady=(0, 5))
        self.website_entry = ttk.Entry(fields_frame, style="Custom.TEntry", font=AppStyles.NORMAL_FONT)
        self.website_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل الإيميل الرسمي
        ttk.Label(fields_frame, text="الإيميل الرسمي:", style="Header.TLabel").pack(anchor=tk.W, pady=(0, 5))
        self.email_entry = ttk.Entry(fields_frame, style="Custom.TEntry", font=AppStyles.NORMAL_FONT)
        self.email_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل رقم واتساب
        ttk.Label(fields_frame, text="رقم هاتف واتساب:", style="Header.TLabel").pack(anchor=tk.W, pady=(0, 5))
        self.whatsapp_entry = ttk.Entry(fields_frame, style="Custom.TEntry", font=AppStyles.NORMAL_FONT)
        self.whatsapp_entry.pack(fill=tk.X, pady=(0, 15))
        
        # ملاحظة الحقول المطلوبة
        note_label = ttk.Label(fields_frame, text="* حقل مطلوب", foreground=AppStyles.DANGER_COLOR)
        note_label.pack(anchor=tk.W, pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # زر الإلغاء
        cancel_btn = ttk.Button(
            buttons_frame, 
            text="إلغاء", 
            command=self.cancel,
            style="Danger.TButton"
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # زر الحفظ
        save_text = "تحديث" if self.source else "حفظ"
        save_btn = ttk.Button(
            buttons_frame, 
            text=save_text, 
            command=self.save,
            style="Success.TButton"
        )
        save_btn.pack(side=tk.RIGHT)
        
        # ربط مفتاح Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
    
    def populate_fields(self):
        """تعبئة الحقول بالبيانات الموجودة"""
        if self.source:
            self.name_entry.insert(0, self.source.name)
            self.website_entry.insert(0, self.source.website)
            self.email_entry.insert(0, self.source.email)
            self.whatsapp_entry.insert(0, self.source.whatsapp)
    
    def validate_fields(self):
        """التحقق من صحة البيانات المدخلة"""
        name = self.name_entry.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "اسم المصدر مطلوب!")
            self.name_entry.focus()
            return False
        
        # التحقق من صحة الإيميل إذا تم إدخاله
        email = self.email_entry.get().strip()
        if email and '@' not in email:
            messagebox.showwarning("تحذير", "تنسيق الإيميل غير صحيح!")
            self.email_entry.focus()
            return False
        
        return True
    
    def save(self):
        """حفظ البيانات"""
        if not self.validate_fields():
            return
        
        # إنشاء كائن المصدر
        source_data = MediaSource(
            id=self.source.id if self.source else None,
            name=self.name_entry.get().strip(),
            website=self.website_entry.get().strip(),
            email=self.email_entry.get().strip(),
            whatsapp=self.whatsapp_entry.get().strip()
        )
        
        self.result = source_data
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result
